# Accept payments with Magpie Checkout (React client)

This is a React version of the HTML + Vanilla JavaScript client implementation. It works with any of the backends in the `server` folder.

## How to run

You will need to run the server and the React client separately.

1. Follow the "How to run" instruction in the `server` directory and make sure that the server is running on http://localhost:4242.

2. Install the dependecies

```bash
npm install
```

3. Start the React client

```bash
npm start
```

- Client running on http://localhost:3000

## Credits

- This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).
