# Running the server

We included several RESTful server that each implement the same endpoints and logic.
Pick the language you are most comfortable in and follow the instructions in the directory on how to run.

# Supported languages

- [JavaScript (Node Express)](node/README.md)

<!-- * [.NET](dotnet/README.md) -->
<!-- * [Go (net/http)](go/README.md) -->
<!-- * [Java (Spark)](java/README.md) -->
<!-- * [PHP (Slim)](php-slim/README.md) -->
<!-- * [PHP](php/README.md) -->
<!-- * [Python (Flask)](python/README.md) -->
<!-- * [<PERSON> (<PERSON>)](ruby/README.md) -->
